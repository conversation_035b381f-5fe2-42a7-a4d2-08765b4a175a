using System;
using System.Collections.Generic;
using FinancialSystem.Domain.Common;
using FinancialSystem.Domain.Enums;

namespace FinancialSystem.Domain.Entities
{
    /// <summary>
    /// Customer entity representing a client of the company
    /// </summary>
    public class Customer : AuditableEntity
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string TaxNumber { get; set; }
        public string ContactPerson { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string Mobile { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Country { get; set; }
        public string PostalCode { get; set; }
        public string Website { get; set; }
        public string Notes { get; set; }
        public CustomerType CustomerType { get; set; }
        public CustomerStatus Status { get; set; }
        public decimal CreditLimit { get; set; }
        public int PaymentTerms { get; set; } // Days
        public decimal CurrentBalance { get; set; }
        
        // Navigation properties
        public ICollection<Invoice> Invoices { get; set; }
        public ICollection<Payment> Payments { get; set; }
    }
}
