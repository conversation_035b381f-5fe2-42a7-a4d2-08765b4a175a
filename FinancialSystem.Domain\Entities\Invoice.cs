using System;
using System.Collections.Generic;
using FinancialSystem.Domain.Common;
using FinancialSystem.Domain.Enums;

namespace FinancialSystem.Domain.Entities
{
    /// <summary>
    /// Invoice entity representing a sales invoice to customers
    /// </summary>
    public class Invoice : AuditableEntity
    {
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public DateTime DueDate { get; set; }
        public Guid CustomerId { get; set; }
        public string CustomerReference { get; set; }
        public string Description { get; set; }
        public decimal SubTotal { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public InvoiceStatus Status { get; set; }
        public string Notes { get; set; }
        
        // Navigation properties
        public Customer Customer { get; set; }
        public ICollection<InvoiceItem> Items { get; set; }
        public ICollection<Payment> Payments { get; set; }
    }
}
