using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;

namespace FinancialSystem.UI
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private DispatcherTimer? _timer;

        public MainWindow()
        {
            InitializeComponent();
            SetupTimer();
        }

        private void SetupTimer()
        {
            _timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _timer.Tick += Timer_Tick;
            _timer.Start();
        }

        private void Timer_Tick(object? sender, EventArgs e)
        {
            try
            {
                // تحديث نص التاريخ والوقت
                DateTimeText.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                Console.WriteLine($"خطأ في تحديث الوقت: {ex.Message}");
                
                // إيقاف المؤقت في حالة حدوث خطأ
                _timer?.Stop();
            }
        }

        private void ExitMenuItem_Click(object sender, RoutedEventArgs e)
        {
            // إغلاق التطبيق
            Close();
        }

        private void NewInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تنفيذ هذه الميزة في الإصدار القادم", "فاتورة جديدة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void NewExpenseButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تنفيذ هذه الميزة في الإصدار القادم", "مصروف جديد", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void NewCustomerButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تنفيذ هذه الميزة في الإصدار القادم", "عميل جديد", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void NewSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تنفيذ هذه الميزة في الإصدار القادم", "مورد جديد", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
