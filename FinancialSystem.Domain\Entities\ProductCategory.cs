using System;
using System.Collections.Generic;
using FinancialSystem.Domain.Common;

namespace FinancialSystem.Domain.Entities
{
    /// <summary>
    /// Product category for grouping products
    /// </summary>
    public class ProductCategory : AuditableEntity
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public Guid? ParentCategoryId { get; set; }
        
        // Navigation properties
        public ProductCategory ParentCategory { get; set; }
        public ICollection<ProductCategory> ChildCategories { get; set; }
        public ICollection<Product> Products { get; set; }
    }
}
