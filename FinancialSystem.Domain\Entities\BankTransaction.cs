using System;
using FinancialSystem.Domain.Common;
using FinancialSystem.Domain.Enums;

namespace FinancialSystem.Domain.Entities
{
    /// <summary>
    /// Bank transaction entity representing transactions in bank accounts
    /// </summary>
    public class BankTransaction : AuditableEntity
    {
        public Guid BankAccountId { get; set; }
        public DateTime TransactionDate { get; set; }
        public string Reference { get; set; }
        public string Description { get; set; }
        public decimal Amount { get; set; }
        public TransactionType TransactionType { get; set; }
        public decimal RunningBalance { get; set; }
        public Guid? PaymentId { get; set; }
        
        // Navigation properties
        public BankAccount BankAccount { get; set; }
        public Payment Payment { get; set; }
    }
}
