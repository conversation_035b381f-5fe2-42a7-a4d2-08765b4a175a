using System;
using System.Collections.Generic;
using FinancialSystem.Domain.Common;
using FinancialSystem.Domain.Enums;

namespace FinancialSystem.Domain.Entities
{
    /// <summary>
    /// Product entity representing goods or services sold to customers
    /// </summary>
    public class Product : AuditableEntity
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public decimal SalesPrice { get; set; }
        public decimal PurchasePrice { get; set; }
        public decimal TaxPercentage { get; set; }
        public bool IsTaxable { get; set; }
        public bool IsActive { get; set; }
        public ProductType ProductType { get; set; }
        public Guid? CategoryId { get; set; }
        
        // Navigation properties
        public ProductCategory Category { get; set; }
        public ICollection<InvoiceItem> InvoiceItems { get; set; }
    }
}
