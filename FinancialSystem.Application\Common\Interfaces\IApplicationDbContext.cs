using System.Threading;
using System.Threading.Tasks;
using FinancialSystem.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace FinancialSystem.Application.Common.Interfaces
{
    /// <summary>
    /// Interface for the application database context
    /// </summary>
    public interface IApplicationDbContext
    {
        DbSet<Customer> Customers { get; set; }
        DbSet<Supplier> Suppliers { get; set; }
        DbSet<Product> Products { get; set; }
        DbSet<ProductCategory> ProductCategories { get; set; }
        DbSet<Invoice> Invoices { get; set; }
        DbSet<InvoiceItem> InvoiceItems { get; set; }
        DbSet<Expense> Expenses { get; set; }
        DbSet<ExpenseItem> ExpenseItems { get; set; }
        DbSet<ExpenseCategory> ExpenseCategories { get; set; }
        DbSet<Payment> Payments { get; set; }
        DbSet<BankAccount> BankAccounts { get; set; }
        DbSet<BankTransaction> BankTransactions { get; set; }
        
        Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    }
}
