﻿<Window x:Class="FinancialSystem.UI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FinancialSystem.UI"
        mc:Ignorable="d"
        Title="نظام المحاسبة المالي" Height="768" Width="1024" WindowStartupLocation="CenterScreen">
    <DockPanel>
        <!-- Main Menu -->
        <Menu DockPanel.Dock="Top">
            <MenuItem Header="ملف">
                <MenuItem Header="إعدادات" />
                <Separator />
                <MenuItem Header="خروج" Click="ExitMenuItem_Click" />
            </MenuItem>
            <MenuItem Header="العملاء">
                <MenuItem Header="قائمة العملاء" />
                <MenuItem Header="إضافة عميل جديد" />
            </MenuItem>
            <MenuItem Header="الموردين">
                <MenuItem Header="قائمة الموردين" />
                <MenuItem Header="إضافة مورد جديد" />
            </MenuItem>
            <MenuItem Header="المبيعات">
                <MenuItem Header="الفواتير" />
                <MenuItem Header="إنشاء فاتورة جديدة" />
            </MenuItem>
            <MenuItem Header="المشتريات">
                <MenuItem Header="المصروفات" />
                <MenuItem Header="إضافة مصروف جديد" />
            </MenuItem>
            <MenuItem Header="المالية">
                <MenuItem Header="الحسابات البنكية" />
                <MenuItem Header="المدفوعات" />
                <MenuItem Header="التقارير المالية" />
            </MenuItem>
            <MenuItem Header="المنتجات">
                <MenuItem Header="قائمة المنتجات" />
                <MenuItem Header="إضافة منتج جديد" />
                <MenuItem Header="فئات المنتجات" />
            </MenuItem>
            <MenuItem Header="التقارير">
                <MenuItem Header="تقارير المبيعات" />
                <MenuItem Header="تقارير المشتريات" />
                <MenuItem Header="تقارير العملاء" />
                <MenuItem Header="تقارير الموردين" />
                <MenuItem Header="تقارير الضرائب" />
            </MenuItem>
            <MenuItem Header="مساعدة">
                <MenuItem Header="حول البرنامج" />
                <MenuItem Header="دليل المستخدم" />
            </MenuItem>
        </Menu>

        <!-- Status Bar -->
        <StatusBar DockPanel.Dock="Bottom" Height="25">
            <StatusBarItem>
                <TextBlock Text="جاهز" />
            </StatusBarItem>
            <Separator />
            <StatusBarItem>
                <TextBlock Text="المستخدم: المدير" />
            </StatusBarItem>
            <Separator />
            <StatusBarItem>
                <TextBlock x:Name="DateTimeText" Text="01/01/2023 12:00:00" />
            </StatusBarItem>
        </StatusBar>

        <!-- Main Content -->
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!-- Dashboard Header -->
            <StackPanel Grid.Row="0" Margin="10">
                <TextBlock Text="لوحة التحكم" FontSize="24" FontWeight="Bold" Margin="0,0,0,10" />
            </StackPanel>

            <!-- Dashboard Content -->
            <Grid Grid.Row="1" Margin="10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!-- Financial Summary -->
                <Border Grid.Column="0" Grid.Row="0" BorderBrush="#CCCCCC" BorderThickness="1" Margin="5" Padding="10" Background="#F9F9F9">
                    <StackPanel>
                        <TextBlock Text="ملخص مالي" FontSize="18" FontWeight="Bold" Margin="0,0,0,10" />
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="إجمالي المبيعات:" Margin="0,5" />
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="0.00" HorizontalAlignment="Right" Margin="0,5" />

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="إجمالي المصروفات:" Margin="0,5" />
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="0.00" HorizontalAlignment="Right" Margin="0,5" />

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="صافي الربح:" Margin="0,5" />
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="0.00" HorizontalAlignment="Right" Margin="0,5" />

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="الرصيد البنكي:" Margin="0,5" />
                            <TextBlock Grid.Row="3" Grid.Column="1" Text="0.00" HorizontalAlignment="Right" Margin="0,5" />
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Recent Invoices -->
                <Border Grid.Column="1" Grid.Row="0" BorderBrush="#CCCCCC" BorderThickness="1" Margin="5" Padding="10" Background="#F9F9F9">
                    <StackPanel>
                        <TextBlock Text="أحدث الفواتير" FontSize="18" FontWeight="Bold" Margin="0,0,0,10" />
                        <DataGrid Height="150">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="رقم الفاتورة" Width="*" />
                                <DataGridTextColumn Header="العميل" Width="*" />
                                <DataGridTextColumn Header="التاريخ" Width="*" />
                                <DataGridTextColumn Header="المبلغ" Width="*" />
                                <DataGridTextColumn Header="الحالة" Width="*" />
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

                <!-- Recent Expenses -->
                <Border Grid.Column="0" Grid.Row="1" BorderBrush="#CCCCCC" BorderThickness="1" Margin="5" Padding="10" Background="#F9F9F9">
                    <StackPanel>
                        <TextBlock Text="أحدث المصروفات" FontSize="18" FontWeight="Bold" Margin="0,0,0,10" />
                        <DataGrid Height="150">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="رقم المصروف" Width="*" />
                                <DataGridTextColumn Header="المورد" Width="*" />
                                <DataGridTextColumn Header="التاريخ" Width="*" />
                                <DataGridTextColumn Header="المبلغ" Width="*" />
                                <DataGridTextColumn Header="الحالة" Width="*" />
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

                <!-- Quick Actions -->
                <Border Grid.Column="1" Grid.Row="1" BorderBrush="#CCCCCC" BorderThickness="1" Margin="5" Padding="10" Background="#F9F9F9">
                    <StackPanel>
                        <TextBlock Text="إجراءات سريعة" FontSize="18" FontWeight="Bold" Margin="0,0,0,10" />
                        <UniformGrid Rows="2" Columns="2">
                            <Button Content="فاتورة جديدة" Margin="5" Padding="10" Click="NewInvoiceButton_Click" />
                            <Button Content="مصروف جديد" Margin="5" Padding="10" Click="NewExpenseButton_Click" />
                            <Button Content="عميل جديد" Margin="5" Padding="10" Click="NewCustomerButton_Click" />
                            <Button Content="مورد جديد" Margin="5" Padding="10" Click="NewSupplierButton_Click" />
                        </UniformGrid>
                    </StackPanel>
                </Border>
            </Grid>
        </Grid>
    </DockPanel>
</Window>
