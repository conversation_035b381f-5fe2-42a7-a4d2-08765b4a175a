using System;
using System.Collections.Generic;
using FinancialSystem.Domain.Common;
using FinancialSystem.Domain.Enums;

namespace FinancialSystem.Domain.Entities
{
    /// <summary>
    /// Supplier entity representing a vendor or supplier to the company
    /// </summary>
    public class Supplier : AuditableEntity
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string TaxNumber { get; set; }
        public string ContactPerson { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string Mobile { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Country { get; set; }
        public string PostalCode { get; set; }
        public string Website { get; set; }
        public string Notes { get; set; }
        public SupplierType SupplierType { get; set; }
        public SupplierStatus Status { get; set; }
        public int PaymentTerms { get; set; } // Days
        public decimal CurrentBalance { get; set; }
        
        // Navigation properties
        public ICollection<Expense> Expenses { get; set; }
        public ICollection<Payment> Payments { get; set; }
    }
}
