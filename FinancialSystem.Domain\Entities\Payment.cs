using System;
using FinancialSystem.Domain.Common;
using FinancialSystem.Domain.Enums;

namespace FinancialSystem.Domain.Entities
{
    /// <summary>
    /// Payment entity representing money received from customers or paid to suppliers
    /// </summary>
    public class Payment : AuditableEntity
    {
        public string PaymentNumber { get; set; }
        public DateTime PaymentDate { get; set; }
        public PaymentType PaymentType { get; set; }
        public PaymentMethod PaymentMethod { get; set; }
        public PaymentDirection Direction { get; set; }
        public decimal Amount { get; set; }
        public string Reference { get; set; }
        public string Description { get; set; }
        public Guid? CustomerId { get; set; }
        public Guid? SupplierId { get; set; }
        public Guid? InvoiceId { get; set; }
        public Guid? ExpenseId { get; set; }
        public Guid? BankAccountId { get; set; }
        
        // Navigation properties
        public Customer Customer { get; set; }
        public Supplier Supplier { get; set; }
        public Invoice Invoice { get; set; }
        public Expense Expense { get; set; }
        public BankAccount BankAccount { get; set; }
    }
}
