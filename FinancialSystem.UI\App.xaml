﻿<Application x:Class="FinancialSystem.UI.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:FinancialSystem.UI"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design Themes -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Teal" SecondaryColor="Amber" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Custom Colors -->
            <SolidColorBrush x:Key="PrimaryHueLightBrush" Color="#B2DFDB"/>
            <SolidColorBrush x:Key="PrimaryHueMidBrush" Color="#009688"/>
            <SolidColorBrush x:Key="PrimaryHueDarkBrush" Color="#00796B"/>
            <SolidColorBrush x:Key="SecondaryAccentBrush" Color="#FFC107"/>
            <SolidColorBrush x:Key="ValidationErrorBrush" Color="#F44336"/>
            <SolidColorBrush x:Key="BackgroundBrush" Color="#FAFAFA"/>
            <SolidColorBrush x:Key="ForegroundBrush" Color="#212121"/>
            <SolidColorBrush x:Key="CardBackgroundBrush" Color="#FFFFFF"/>
            <SolidColorBrush x:Key="DividerBrush" Color="#BDBDBD"/>

            <!-- Application-wide styles -->
            <Style TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="Margin" Value="5"/>
                <Setter Property="MinWidth" Value="100"/>
                <Setter Property="FontWeight" Value="Medium"/>
            </Style>

            <Style TargetType="TextBox" BasedOn="{StaticResource MaterialDesignTextBox}">
                <Setter Property="Margin" Value="5"/>
                <Setter Property="FontSize" Value="14"/>
            </Style>

            <Style TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignComboBox}">
                <Setter Property="Margin" Value="5"/>
                <Setter Property="FontSize" Value="14"/>
            </Style>

            <Style TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
                <Setter Property="Margin" Value="5"/>
                <Setter Property="AutoGenerateColumns" Value="False"/>
                <Setter Property="IsReadOnly" Value="True"/>
                <Setter Property="CanUserAddRows" Value="False"/>
                <Setter Property="CanUserDeleteRows" Value="False"/>
                <Setter Property="CanUserReorderColumns" Value="True"/>
                <Setter Property="CanUserResizeColumns" Value="True"/>
                <Setter Property="CanUserSortColumns" Value="True"/>
                <Setter Property="SelectionMode" Value="Single"/>
                <Setter Property="SelectionUnit" Value="FullRow"/>
                <Setter Property="AlternatingRowBackground" Value="{StaticResource PrimaryHueLightBrush}"/>
                <Setter Property="RowHeaderWidth" Value="0"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
                <Setter Property="HorizontalGridLinesBrush" Value="{StaticResource DividerBrush}"/>
                <Setter Property="VerticalGridLinesBrush" Value="{StaticResource DividerBrush}"/>
            </Style>

            <!-- Card Style -->
            <Style x:Key="MaterialCard" TargetType="Border">
                <Setter Property="Background" Value="{StaticResource CardBackgroundBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="CornerRadius" Value="4"/>
                <Setter Property="Padding" Value="16"/>
                <Setter Property="Margin" Value="8"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect BlurRadius="10" ShadowDepth="1" Direction="270" Color="#22000000" Opacity="0.3"/>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Section Header Style -->
            <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="20"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryHueDarkBrush}"/>
                <Setter Property="Margin" Value="0,0,0,16"/>
            </Style>

            <!-- Dashboard Tile Style -->
            <Style x:Key="DashboardTileStyle" TargetType="Border">
                <Setter Property="Background" Value="{StaticResource PrimaryHueMidBrush}"/>
                <Setter Property="CornerRadius" Value="4"/>
                <Setter Property="Padding" Value="16"/>
                <Setter Property="Margin" Value="8"/>
                <Setter Property="MinHeight" Value="120"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect BlurRadius="10" ShadowDepth="1" Direction="270" Color="#22000000" Opacity="0.3"/>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Dashboard Tile Text Style -->
            <Style x:Key="DashboardTileTextStyle" TargetType="TextBlock">
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="FontSize" Value="16"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="TextWrapping" Value="Wrap"/>
            </Style>

            <!-- Dashboard Tile Value Style -->
            <Style x:Key="DashboardTileValueStyle" TargetType="TextBlock">
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="FontSize" Value="28"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Margin" Value="0,8,0,0"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
