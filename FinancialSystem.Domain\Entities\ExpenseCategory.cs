using System;
using System.Collections.Generic;
using FinancialSystem.Domain.Common;

namespace FinancialSystem.Domain.Entities
{
    /// <summary>
    /// Expense category for grouping expenses
    /// </summary>
    public class ExpenseCategory : AuditableEntity
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public Guid? ParentCategoryId { get; set; }
        
        // Navigation properties
        public ExpenseCategory ParentCategory { get; set; }
        public ICollection<ExpenseCategory> ChildCategories { get; set; }
        public ICollection<Expense> Expenses { get; set; }
    }
}
