using System;
using FinancialSystem.Domain.Common;

namespace FinancialSystem.Domain.Entities
{
    /// <summary>
    /// Invoice item representing a line item in an invoice
    /// </summary>
    public class InvoiceItem : BaseEntity
    {
        public Guid InvoiceId { get; set; }
        public Guid? ProductId { get; set; }
        public string Description { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountPercentage { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxPercentage { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal SubTotal { get; set; }
        public decimal TotalAmount { get; set; }
        
        // Navigation properties
        public Invoice Invoice { get; set; }
        public Product Product { get; set; }
    }
}
