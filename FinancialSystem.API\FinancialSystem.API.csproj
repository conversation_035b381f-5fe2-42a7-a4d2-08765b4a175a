<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net10.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="10.0.0-preview.2.25164.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FinancialSystem.Application\FinancialSystem.Application.csproj" />
    <ProjectReference Include="..\FinancialSystem.Infrastructure\FinancialSystem.Infrastructure.csproj" />
    <ProjectReference Include="..\FinancialSystem.Persistence\FinancialSystem.Persistence.csproj" />
    <ProjectReference Include="..\FinancialSystem.Common\FinancialSystem.Common.csproj" />
  </ItemGroup>

</Project>
