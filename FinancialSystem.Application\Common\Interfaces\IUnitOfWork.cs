using System;
using System.Threading;
using System.Threading.Tasks;
using FinancialSystem.Domain.Entities;

namespace FinancialSystem.Application.Common.Interfaces
{
    /// <summary>
    /// Unit of work interface for managing transactions
    /// </summary>
    public interface IUnitOfWork : IDisposable
    {
        IRepository<Customer> Customers { get; }
        IRepository<Supplier> Suppliers { get; }
        IRepository<Product> Products { get; }
        IRepository<ProductCategory> ProductCategories { get; }
        IRepository<Invoice> Invoices { get; }
        IRepository<InvoiceItem> InvoiceItems { get; }
        IRepository<Expense> Expenses { get; }
        IRepository<ExpenseItem> ExpenseItems { get; }
        IRepository<ExpenseCategory> ExpenseCategories { get; }
        IRepository<Payment> Payments { get; }
        IRepository<BankAccount> BankAccounts { get; }
        IRepository<BankTransaction> BankTransactions { get; }
        
        Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
        Task BeginTransactionAsync(CancellationToken cancellationToken = default);
        Task CommitTransactionAsync(CancellationToken cancellationToken = default);
        Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
    }
}
