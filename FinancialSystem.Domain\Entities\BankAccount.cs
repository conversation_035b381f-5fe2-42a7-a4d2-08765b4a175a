using System;
using System.Collections.Generic;
using FinancialSystem.Domain.Common;
using FinancialSystem.Domain.Enums;

namespace FinancialSystem.Domain.Entities
{
    /// <summary>
    /// Bank account entity representing company bank accounts
    /// </summary>
    public class BankAccount : AuditableEntity
    {
        public string AccountName { get; set; }
        public string AccountNumber { get; set; }
        public string BankName { get; set; }
        public string BranchName { get; set; }
        public string SwiftCode { get; set; }
        public string IBAN { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public decimal CurrentBalance { get; set; }
        public bool IsActive { get; set; }
        public string Description { get; set; }
        
        // Navigation properties
        public ICollection<Payment> Payments { get; set; }
        public ICollection<BankTransaction> Transactions { get; set; }
    }
}
