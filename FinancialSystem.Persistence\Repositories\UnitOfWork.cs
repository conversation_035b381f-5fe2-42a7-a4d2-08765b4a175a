using System;
using System.Threading;
using System.Threading.Tasks;
using FinancialSystem.Application.Common.Interfaces;
using FinancialSystem.Domain.Entities;
using Microsoft.EntityFrameworkCore.Storage;

namespace FinancialSystem.Persistence.Repositories
{
    /// <summary>
    /// Unit of work implementation for managing transactions
    /// </summary>
    public class UnitOfWork : IUnitOfWork
    {
        private readonly ApplicationDbContext _dbContext;
        private IDbContextTransaction? _transaction;
        private bool _disposed;

        public IRepository<Customer> Customers { get; }
        public IRepository<Supplier> Suppliers { get; }
        public IRepository<Product> Products { get; }
        public IRepository<ProductCategory> ProductCategories { get; }
        public IRepository<Invoice> Invoices { get; }
        public IRepository<InvoiceItem> InvoiceItems { get; }
        public IRepository<Expense> Expenses { get; }
        public IRepository<ExpenseItem> ExpenseItems { get; }
        public IRepository<ExpenseCategory> ExpenseCategories { get; }
        public IRepository<Payment> Payments { get; }
        public IRepository<BankAccount> BankAccounts { get; }
        public IRepository<BankTransaction> BankTransactions { get; }

        public UnitOfWork(ApplicationDbContext dbContext)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));

            Customers = new Repository<Customer>(_dbContext);
            Suppliers = new Repository<Supplier>(_dbContext);
            Products = new Repository<Product>(_dbContext);
            ProductCategories = new Repository<ProductCategory>(_dbContext);
            Invoices = new Repository<Invoice>(_dbContext);
            InvoiceItems = new Repository<InvoiceItem>(_dbContext);
            Expenses = new Repository<Expense>(_dbContext);
            ExpenseItems = new Repository<ExpenseItem>(_dbContext);
            ExpenseCategories = new Repository<ExpenseCategory>(_dbContext);
            Payments = new Repository<Payment>(_dbContext);
            BankAccounts = new Repository<BankAccount>(_dbContext);
            BankTransactions = new Repository<BankTransaction>(_dbContext);
        }

        public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            return await _dbContext.SaveChangesAsync(cancellationToken);
        }

        public async Task BeginTransactionAsync(CancellationToken cancellationToken = default)
        {
            _transaction = await _dbContext.Database.BeginTransactionAsync(cancellationToken);
        }

        public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
        {
            if (_transaction == null)
                throw new InvalidOperationException("No transaction has been started.");

            try
            {
                await _dbContext.SaveChangesAsync(cancellationToken);
                await _transaction.CommitAsync(cancellationToken);
            }
            catch
            {
                await RollbackTransactionAsync(cancellationToken);
                throw;
            }
            finally
            {
                if (_transaction != null)
                {
                    await _transaction.DisposeAsync();
                    _transaction = null;
                }
            }
        }

        public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
        {
            if (_transaction != null)
            {
                await _transaction.RollbackAsync(cancellationToken);
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _dbContext.Dispose();
                _transaction?.Dispose();
            }
            _disposed = true;
        }
    }
}
