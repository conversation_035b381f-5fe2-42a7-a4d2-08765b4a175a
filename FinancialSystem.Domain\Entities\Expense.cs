using System;
using System.Collections.Generic;
using FinancialSystem.Domain.Common;
using FinancialSystem.Domain.Enums;

namespace FinancialSystem.Domain.Entities
{
    /// <summary>
    /// Expense entity representing costs incurred by the company
    /// </summary>
    public class Expense : AuditableEntity
    {
        public string ExpenseNumber { get; set; }
        public DateTime ExpenseDate { get; set; }
        public DateTime DueDate { get; set; }
        public Guid? SupplierId { get; set; }
        public string SupplierReference { get; set; }
        public string Description { get; set; }
        public decimal SubTotal { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public ExpenseStatus Status { get; set; }
        public Guid? ExpenseCategoryId { get; set; }
        public string Notes { get; set; }
        
        // Navigation properties
        public Supplier Supplier { get; set; }
        public ExpenseCategory ExpenseCategory { get; set; }
        public ICollection<ExpenseItem> Items { get; set; }
        public ICollection<Payment> Payments { get; set; }
    }
}
