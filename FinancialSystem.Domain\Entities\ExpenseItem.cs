using System;
using FinancialSystem.Domain.Common;

namespace FinancialSystem.Domain.Entities
{
    /// <summary>
    /// Expense item representing a line item in an expense
    /// </summary>
    public class ExpenseItem : BaseEntity
    {
        public Guid ExpenseId { get; set; }
        public string Description { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TaxPercentage { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal SubTotal { get; set; }
        public decimal TotalAmount { get; set; }
        
        // Navigation properties
        public Expense Expense { get; set; }
    }
}
