﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net10.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.2" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageReference Include="xunit" Version="2.9.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FinancialSystem.Domain\FinancialSystem.Domain.csproj" />
    <ProjectReference Include="..\FinancialSystem.Application\FinancialSystem.Application.csproj" />
    <ProjectReference Include="..\FinancialSystem.Infrastructure\FinancialSystem.Infrastructure.csproj" />
    <ProjectReference Include="..\FinancialSystem.Persistence\FinancialSystem.Persistence.csproj" />
    <ProjectReference Include="..\FinancialSystem.Common\FinancialSystem.Common.csproj" />
  </ItemGroup>

</Project>
